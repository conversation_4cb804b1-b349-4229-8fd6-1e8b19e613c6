import customtkinter as ctk
import ollama
import threading
import time
from queue import Queue, Empty

# ==================================================================================================
# Conversation Manager: Handles the backend logic of the AI debate
# ==================================================================================================
class ConversationManager:
    """
    Manages the conversation loop between two Ollama models in a separate thread.
    """
    def __init__(self, model1_name, model2_name, system_prompt1, system_prompt2, initial_topic, update_queue):
        self.model1_name = model1_name
        self.model2_name = model2_name
        self.system_prompt1 = system_prompt1
        self.system_prompt2 = system_prompt2
        self.initial_topic = initial_topic
        self.update_queue = update_queue  # Queue to send UI updates

        self.conversation_thread = None
        self.stop_event = threading.Event()

        # Each model gets its own message history to maintain context
        self.messages1 = [{"role": "system", "content": self.system_prompt1}]
        self.messages2 = [{"role": "system", "content": self.system_prompt2}]

    def start(self):
        """Starts the conversation in a new thread."""
        if self.conversation_thread is None or not self.conversation_thread.is_alive():
            self.stop_event.clear()
            self.conversation_thread = threading.Thread(target=self._run_conversation_loop, daemon=True)
            self.conversation_thread.start()
            self.update_queue.put(("status", "المحادثة بدأت..."))

    def stop(self):
        """Signals the conversation loop to stop."""
        if self.conversation_thread and self.conversation_thread.is_alive():
            self.stop_event.set()
            self.update_queue.put(("status", "جاري إيقاف المحادثة..."))

    def _run_conversation_loop(self):
        """The main loop where the two models talk to each other."""
        try:
            # Start the conversation with the initial topic
            self.update_queue.put(("message", "الموضوع", self.initial_topic))
            
            # إضافة توجيه للنموذج الأول ليبدأ الحديث
            start_prompt = f"""الموضوع هو: {self.initial_topic}

قم بطرح وجهة نظرك حول هذا الموضوع وافتتح النقاش. كن واضحاً ومباشراً في عرض رأيك."""

            # Add the initial prompt to the first model's history
            self.messages1.append({"role": "user", "content": start_prompt})

            while not self.stop_event.is_set():
                # --- Model 1's Turn ---
                if self.stop_event.is_set(): break
                self.update_queue.put(("status", f"نموذج 1 ({self.model1_name}) يكتب..."))
                
                response1_content = self._get_model_response(self.model1_name, self.messages1)
                if not response1_content: break # Stop if there was an error

                self.messages1.append({"role": "assistant", "content": response1_content})
                self.update_queue.put(("message", self.model1_name, response1_content))
                
                # The output of model 1 is the input for model 2
                self.messages2.append({"role": "user", "content": response1_content})

                time.sleep(1) # Small delay

                # --- Model 2's Turn ---
                if self.stop_event.is_set(): break
                self.update_queue.put(("status", f"نموذج 2 ({self.model2_name}) يكتب..."))

                response2_content = self._get_model_response(self.model2_name, self.messages2)
                if not response2_content: break # Stop if there was an error

                self.messages2.append({"role": "assistant", "content": response2_content})
                self.update_queue.put(("message", self.model2_name, response2_content))

                # The output of model 2 is the input for model 1
                self.messages1.append({"role": "user", "content": response2_content})
                
                time.sleep(1) # Small delay

        except Exception as e:
            self.update_queue.put(("error", f"حدث خطأ فادح: {e}"))
        finally:
            self.update_queue.put(("status", "المحادثة توقفت."))
            self.stop_event.clear()

    def _get_model_response(self, model_name, messages):
        """Helper function to call the Ollama API and handle potential errors."""
        try:
            self.update_queue.put(("status", f"جاري الاتصال بنموذج {model_name}..."))
            full_response = ""
            
            # استخدام Stream للحصول على الرد كلمة بكلمة
            for response in ollama.chat(
                model=model_name,
                messages=messages,
                stream=True
            ):
                if self.stop_event.is_set():
                    break
                    
                if 'message' in response:
                    chunk = response['message'].get('content', '')
                    if chunk:
                        full_response += chunk
                        # إرسال كل جزء مباشرة إلى واجهة المستخدم
                        self.update_queue.put(("chunk", (model_name, chunk)))
            
            return full_response
        except ollama.ResponseError as e:
            error_message = f"خطأ في النموذج {model_name}: {e.error}"
            if "model not found" in e.error:
                error_message += "\nتأكد من أن النموذج مُثبّت ومتاح في Ollama."
            self.update_queue.put(("error", error_message))
            self.stop_event.set() # Stop the loop on error
            return None

# ==================================================================================================
# Main Application GUI
# ==================================================================================================
class OllamaDebateApp(ctk.CTk):
    def __init__(self):
        super().__init__()

        self.title("Ollama Debate Arena | ساحة نقاش أولاما")
        self.geometry("900x700")
        ctk.set_appearance_mode("Dark")
        ctk.set_default_color_theme("blue")

        self.grid_columnconfigure(0, weight=1)
        self.grid_rowconfigure(1, weight=1)

        self.conversation_manager = None
        self.update_queue = Queue()

        self._create_widgets()
        self.process_queue() # Start listening for UI updates

    def _create_widgets(self):
        # --- Configuration Frame ---
        config_frame = ctk.CTkFrame(self)
        config_frame.grid(row=0, column=0, padx=10, pady=10, sticky="ew")
        config_frame.grid_columnconfigure((1, 3), weight=1)

        # Model 1
        ctk.CTkLabel(config_frame, text="نموذج 1", font=ctk.CTkFont(weight="bold")).grid(row=0, column=0, padx=10, pady=5)
        self.model1_name_entry = ctk.CTkEntry(config_frame, placeholder_text="e.g., gemma3:270m")
        self.model1_name_entry.grid(row=0, column=1, padx=10, pady=5, sticky="ew")
        self.model1_name_entry.insert(0, "gemma3:270m")

        self.model1_prompt_entry = ctk.CTkEntry(config_frame, placeholder_text="مهمة/شخصية نموذج 1 (System Prompt)")
        self.model1_prompt_entry.grid(row=1, column=0, columnspan=2, padx=10, pady=5, sticky="ew")
        self.model1_prompt_entry.insert(0, "أنت خبير استراتيجي تؤيد السياسات المصرية وتدافع عنها بحماس. عليك أن تبدأ النقاش بطرح وجهة نظرك بشكل واضح ومباشر.")

        # Model 2
        ctk.CTkLabel(config_frame, text="نموذج 2", font=ctk.CTkFont(weight="bold")).grid(row=0, column=2, padx=10, pady=5)
        self.model2_name_entry = ctk.CTkEntry(config_frame, placeholder_text="e.g., gemma3:270m")
        self.model2_name_entry.grid(row=0, column=3, padx=10, pady=5, sticky="ew")
        self.model2_name_entry.insert(0, "gemma3:270m")

        self.model2_prompt_entry = ctk.CTkEntry(config_frame, placeholder_text="مهمة/شخصية نموذج 2 (System Prompt)")
        self.model2_prompt_entry.grid(row=1, column=2, columnspan=2, padx=10, pady=5, sticky="ew")
        self.model2_prompt_entry.insert(0, "أنت محلل سياسي أمريكي وترى أن السياسات الأمريكية هي الأفضل عالميًا.")

        # Topic
        ctk.CTkLabel(config_frame, text="موضوع النقاش", font=ctk.CTkFont(weight="bold")).grid(row=2, column=0, padx=10, pady=5)
        self.topic_entry = ctk.CTkEntry(config_frame, placeholder_text="اكتب هنا الموضوع الذي سيبدأ به النقاش")
        self.topic_entry.grid(row=2, column=1, columnspan=3, padx=10, pady=5, sticky="ew")
        self.topic_entry.insert(0, "مقارنة بين الصناعات العسكرية في مصر والولايات المتحدة الأمريكية.")

        # --- Conversation Display Frame ---
        display_frame = ctk.CTkFrame(self)
        display_frame.grid(row=1, column=0, padx=10, pady=10, sticky="nsew")
        display_frame.grid_rowconfigure(0, weight=1)
        display_frame.grid_columnconfigure(0, weight=1)

        self.conversation_textbox = ctk.CTkTextbox(display_frame, state="normal", wrap="word", font=("Arial", 14))
        self.conversation_textbox.configure(width=800, height=400)
        self.conversation_textbox.grid(row=0, column=0, sticky="nsew")
        
        # Configure tags for coloring
        self.conversation_textbox.tag_config("model1", foreground="#3498db") # Blue
        self.conversation_textbox.tag_config("model2", foreground="#2ecc71") # Green
        self.conversation_textbox.tag_config("topic", foreground="#f1c40f") # Yellow
        self.conversation_textbox.tag_config("error", foreground="#e74c3c") # Red

        # --- Control Frame ---
        control_frame = ctk.CTkFrame(self)
        control_frame.grid(row=2, column=0, padx=10, pady=10, sticky="ew")
        control_frame.grid_columnconfigure((0, 1), weight=1)

        self.start_button = ctk.CTkButton(control_frame, text="ابدأ النقاش", command=self.start_conversation)
        self.start_button.grid(row=0, column=0, padx=5, pady=5, sticky="ew")

        self.stop_button = ctk.CTkButton(control_frame, text="أوقف النقاش", command=self.stop_conversation, state="disabled")
        self.stop_button.grid(row=0, column=1, padx=5, pady=5, sticky="ew")
        
        # --- Status Bar ---
        self.status_label = ctk.CTkLabel(self, text="جاهز للبدء", anchor="w")
        self.status_label.grid(row=3, column=0, padx=10, pady=5, sticky="ew")

    def start_conversation(self):
        """Validates inputs and starts the conversation."""
        # Clear previous conversation
        self.conversation_textbox.configure(state="normal")
        self.conversation_textbox.delete("1.0", "end")
        
        # Add initial status message
        self.conversation_textbox.insert("end", "جاري بدء المحادثة...\n", "topic")
        self.update()

        # Get all values from UI
        model1 = self.model1_name_entry.get()
        model2 = self.model2_name_entry.get()
        prompt1 = self.model1_prompt_entry.get()
        prompt2 = self.model2_prompt_entry.get()
        topic = self.topic_entry.get()

        if not all([model1, model2, prompt1, prompt2, topic]):
            self.update_status("خطأ: يرجى ملء جميع الحقول.", "error")
            return

        self.start_button.configure(state="disabled")
        self.stop_button.configure(state="normal")

        self.conversation_manager = ConversationManager(
            model1_name=model1,
            model2_name=model2,
            system_prompt1=prompt1,
            system_prompt2=prompt2,
            initial_topic=topic,
            update_queue=self.update_queue
        )
        self.conversation_manager.start()

    def stop_conversation(self):
        """Stops the conversation."""
        if self.conversation_manager:
            self.conversation_manager.stop()
        self.start_button.configure(state="normal")
        self.stop_button.configure(state="disabled")

    def process_queue(self):
        """Checks the queue for messages from the conversation thread and updates the UI."""
        try:
            while not self.update_queue.empty():
                item = self.update_queue.get_nowait()
                if len(item) != 2:
                    continue
                    
                message_type, data = item
                
                if message_type == "message":
                    if isinstance(data, tuple) and len(data) == 2:
                        speaker, text = data
                        tag = "model1" if speaker == self.model1_name_entry.get() else "model2"
                        if speaker == "الموضوع":
                            tag = "topic"
                        self.add_message_to_display(f"\n{speaker}:\n", tag)
                        self.add_message_to_display(f"{text}\n\n")
                        print(f"Added message from {speaker}: {text}")  # Debug message
                
                elif message_type == "chunk":
                    if isinstance(data, tuple) and len(data) == 2:
                        speaker, chunk = data
                        tag = "model1" if speaker == self.model1_name_entry.get() else "model2"
                        self.add_message_to_display(chunk)
                        print(f"Added chunk from {speaker}: {chunk}")  # Debug message
                
                elif message_type == "status":
                    self.update_status(data)
                    if data == "المحادثة توقفت.":
                        self.start_button.configure(state="normal")
                        self.stop_button.configure(state="disabled")
    
                elif message_type == "error":
                    self.add_message_to_display(f"خطأ:\n{data}\n\n", "error", bold=True)
                    self.update_status("توقفت المحادثة بسبب خطأ.", "error")
                    self.start_button.configure(state="normal")
                    self.stop_button.configure(state="disabled")

        except Empty:
            pass
        finally:
            # Reschedule to run again after 100ms
            self.after(100, self.process_queue)

    def add_message_to_display(self, text, tag=None, bold=False):
        """Appends a message to the conversation textbox with optional styling."""
        self.conversation_textbox.configure(state="normal")  # Enable editing
        
        if tag:
            self.conversation_textbox.insert("end", text, tag)
        else:
            self.conversation_textbox.insert("end", text)
        
        self.conversation_textbox.see("end")  # Auto-scroll
        self.update()

    def update_status(self, text, level="info"):
        """Updates the status bar label."""
        color = "white"
        if level == "error":
            color = "#e74c3c" # Red
        self.status_label.configure(text=text, text_color=color)

    def on_closing(self):
        """Handle window closing."""
        if self.conversation_manager:
            self.conversation_manager.stop()
        self.destroy()

if __name__ == "__main__":
    app = OllamaDebateApp()
    app.protocol("WM_DELETE_WINDOW", app.on_closing)
    app.mainloop()
